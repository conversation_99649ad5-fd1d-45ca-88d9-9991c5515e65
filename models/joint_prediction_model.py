"""
6台空调联合预测模型
实现空调间相互影响的机器学习预测系统
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
import xarray as xr
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class AirConditionerJointPredictor:
    """6台空调联合预测器"""
    
    def __init__(self, data_precision=np.float16, device='cpu'):
        self.data_precision = data_precision
        self.device = device
        self.num_ac = 6  # 6台空调
        self.num_sensors = 48  # 48个温湿度传感器
        
        # 数据标准化器
        self.input_scaler = StandardScaler()
        self.output_scaler = StandardScaler()
        
        # 模型组件
        self.neural_network = None
        self.weight_matrix = None
        self.is_trained = False
        
        # 历史数据存储
        self.history_buffer = []
        self.max_history = 100  # 保存最近100条记录用于在线学习
        
        # 初始化权重矩阵 - 48个传感器对6台空调的影响权重
        self._initialize_weights()
        
    def _initialize_weights(self):
        """初始化权重矩阵：48个传感器对6台空调的影响权重"""
        # 初始权重设为相等，每个传感器对每台空调的影响权重为1/48
        self.weight_matrix = np.ones((self.num_sensors, self.num_ac), dtype=self.data_precision) / self.num_sensors
        
    def _process_energy_consumption_mapping(self, energy_data: List) -> List:
        """
        处理能耗数据的特殊映射关系
        输入：3个能耗数据源
        输出：6台空调对应的能耗数据
        """
        if len(energy_data) != 3:
            raise ValueError(f"能耗数据应该有3个数据源，实际收到{len(energy_data)}个")
            
        # 根据配置文件注释的映射关系
        mapped_energy = [
            energy_data[0],  # 艾特网能空调1# -> 第1个数据源
            energy_data[1],  # 艾特网能空调2# -> 第2个数据源（与美的空调共用）
            energy_data[1],  # 美的空调 -> 第2个数据源（与艾特网能空调2#共用）
            energy_data[2],  # 维谛精密空调1 -> 第3个数据源（三台维谛共用）
            energy_data[2],  # 维谛空调2 -> 第3个数据源（三台维谛共用）
            energy_data[2],  # 维谛空调3 -> 第3个数据源（三台维谛共用）
        ]
        
        return mapped_energy
        
    def _process_cooling_mode_mapping(self, cooling_mode_data: List) -> List:
        """
        处理制冷模式数据的特殊映射关系
        输入：8个制冷模式UID（艾特网能空调每台2个，其他空调每台1个）
        输出：6台空调对应的制冷模式数据
        """
        if len(cooling_mode_data) != 8:
            raise ValueError(f"制冷模式数据应该有8个UID，实际收到{len(cooling_mode_data)}个")
            
        # 根据配置文件的映射关系
        mapped_cooling = [
            (cooling_mode_data[0] + cooling_mode_data[1]) / 2,  # 艾特网能空调1# (平均两个系统)
            (cooling_mode_data[2] + cooling_mode_data[3]) / 2,  # 艾特网能空调2# (平均两个系统)
            cooling_mode_data[4],  # 美的空调
            cooling_mode_data[5],  # 维谛精密空调1
            cooling_mode_data[6],  # 维谛空调2
            cooling_mode_data[7],  # 维谛空调3
        ]
        
        return mapped_cooling
        
    def _create_feature_matrix(self, processed_data: Dict) -> np.ndarray:
        """
        创建特征矩阵，包含空调间相互影响的特征
        """
        features = []
        
        # 基础特征：每台空调的直接参数
        for i in range(self.num_ac):
            ac_features = [
                processed_data['air_conditioner_setting_temperature'][i],
                processed_data['air_conditioner_setting_humidity'][i],
                processed_data['air_conditioner_cooling_mode'][i],
            ]
            features.extend(ac_features)
            
        # 环境特征：温湿度传感器数据加权平均
        temp_sensors = np.array(processed_data['indoor_temperature_sensor'])
        humidity_sensors = np.array(processed_data['indoor_humidity_sensor'])
        
        # 为每台空调计算加权的环境特征
        for i in range(self.num_ac):
            # 使用权重矩阵计算每台空调感受到的环境温湿度
            weighted_temp = np.sum(temp_sensors * self.weight_matrix[:, i])
            weighted_humidity = np.sum(humidity_sensors * self.weight_matrix[:, i])
            features.extend([weighted_temp, weighted_humidity])
            
        # 交互特征：空调间的相对设定差异
        for i in range(self.num_ac):
            for j in range(i+1, self.num_ac):
                temp_diff = abs(processed_data['air_conditioner_setting_temperature'][i] - 
                              processed_data['air_conditioner_setting_temperature'][j])
                humidity_diff = abs(processed_data['air_conditioner_setting_humidity'][i] - 
                                  processed_data['air_conditioner_setting_humidity'][j])
                features.extend([temp_diff, humidity_diff])
                
        return np.array(features, dtype=self.data_precision)
        
    def _create_target_matrix(self, processed_data: Dict) -> np.ndarray:
        """创建目标矩阵（6台空调的能耗和室内温度）"""
        targets = []
        
        # 能耗目标
        energy_targets = processed_data['energy_consumption_collection']
        targets.extend(energy_targets)
        
        # 室内温度目标（使用加权平均）
        temp_sensors = np.array(processed_data['indoor_temperature_sensor'])
        for i in range(self.num_ac):
            weighted_temp = np.sum(temp_sensors * self.weight_matrix[:, i])
            targets.append(weighted_temp)
            
        return np.array(targets, dtype=self.data_precision)


class JointPredictionNeuralNetwork(nn.Module):
    """联合预测神经网络"""
    
    def __init__(self, input_size: int, hidden_size: int = 128, num_ac: int = 6):
        super().__init__()
        self.num_ac = num_ac
        
        # 编码器：提取共享特征
        self.encoder = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
        )
        
        # 注意力机制：学习空调间的相互影响
        self.attention = nn.MultiheadAttention(hidden_size, num_heads=8, batch_first=True)
        
        # 解码器：为每台空调生成预测
        self.decoder = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, 2)  # 每台空调输出2个值：能耗和温度
        )
        
    def forward(self, x):
        batch_size = x.size(0)
        
        # 编码
        encoded = self.encoder(x)  # [batch_size, hidden_size]
        
        # 为每台空调复制编码特征
        ac_features = encoded.unsqueeze(1).repeat(1, self.num_ac, 1)  # [batch_size, num_ac, hidden_size]
        
        # 注意力机制学习空调间相互影响
        attended_features, _ = self.attention(ac_features, ac_features, ac_features)
        
        # 解码：为每台空调生成预测
        predictions = []
        for i in range(self.num_ac):
            ac_pred = self.decoder(attended_features[:, i, :])  # [batch_size, 2]
            predictions.append(ac_pred)
            
        # 拼接所有空调的预测结果
        output = torch.cat(predictions, dim=1)  # [batch_size, num_ac * 2]
        
        return output


class WeightLearner:
    """权重学习器：学习48个传感器对6台空调的影响权重"""
    
    def __init__(self, num_sensors: int = 48, num_ac: int = 6):
        self.num_sensors = num_sensors
        self.num_ac = num_ac
        self.weight_models = {}
        
        # 为每台空调创建一个随机森林模型来学习传感器权重
        for i in range(num_ac):
            self.weight_models[f'ac_{i}'] = RandomForestRegressor(
                n_estimators=50,
                max_depth=10,
                random_state=42
            )
            
    def learn_weights(self, sensor_data: np.ndarray, ac_performance: np.ndarray) -> np.ndarray:
        """
        学习传感器权重
        
        Args:
            sensor_data: [n_samples, num_sensors] 传感器数据
            ac_performance: [n_samples, num_ac] 空调性能数据
            
        Returns:
            weight_matrix: [num_sensors, num_ac] 权重矩阵
        """
        weight_matrix = np.zeros((self.num_sensors, self.num_ac))
        
        for i in range(self.num_ac):
            # 训练模型
            self.weight_models[f'ac_{i}'].fit(sensor_data, ac_performance[:, i])
            
            # 获取特征重要性作为权重
            importance = self.weight_models[f'ac_{i}'].feature_importances_
            
            # 归一化权重
            weight_matrix[:, i] = importance / np.sum(importance)
            
        return weight_matrix.astype(np.float16)
